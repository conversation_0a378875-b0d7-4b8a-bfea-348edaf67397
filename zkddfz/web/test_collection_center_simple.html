<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子模型集合中心点修复验证</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .problem-list, .solution-list {
            list-style: none;
            padding: 0;
        }
        
        .problem-list li, .solution-list li {
            padding: 10px;
            margin: 8px 0;
            border-radius: 4px;
        }
        
        .problem-list li {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .solution-list li {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison .old, .comparison .new {
            padding: 15px;
            border-radius: 8px;
        }
        
        .comparison .old {
            background: #fff3cd;
            border: 1px solid #ffc107;
        }
        
        .comparison .new {
            background: #d4edda;
            border: 1px solid #28a745;
        }
        
        .test-button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>子模型集合中心点修复说明</h1>
        
        <div class="section">
            <h3>🔍 问题分析</h3>
            <p>当mesh名称对应的是一个<span class="highlight">子模型集合</span>而不是单个mesh时，原来的中心点计算方法存在以下问题：</p>
            <ul class="problem-list">
                <li><strong>简单平均法不准确</strong>：只计算所有子mesh中心的平均值，忽略了每个mesh的实际大小和重要性</li>
                <li><strong>坐标变换不完整</strong>：没有正确处理完整的父子层级变换链，导致世界坐标计算错误</li>
                <li><strong>包围盒计算错误</strong>：没有计算整个子模型集合的总包围盒，而是简单地平均各个子包围盒</li>
                <li><strong>缓存机制缺失</strong>：每次旋转都重新计算中心点，影响性能</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>✅ 修复方案</h3>
            <ul class="solution-list">
                <li><strong>真实几何中心计算</strong>：基于整个子模型集合的总包围盒计算真实的几何中心点</li>
                <li><strong>完整世界坐标变换</strong>：正确处理所有子对象的世界坐标变换矩阵</li>
                <li><strong>智能缓存机制</strong>：缓存计算结果，避免重复计算，提高性能</li>
                <li><strong>自动更新检测</strong>：检测模型结构变化，自动更新缓存数据</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🔄 方法对比</h3>
            <div class="comparison">
                <div class="old">
                    <h4>修复前（错误方法）</h4>
                    <div class="code-block">
// 简单平均所有子mesh的中心点
let totalCenter = new Vector3();
meshes.forEach(mesh => {
    const center = new Vector3();
    mesh.geometry.boundingBox.getCenter(center);
    totalCenter.add(center);
});
totalCenter.divideScalar(meshes.length);
                    </div>
                    <p><span class="warning">问题</span>：不考虑mesh大小，结果不准确</p>
                </div>
                
                <div class="new">
                    <h4>修复后（正确方法）</h4>
                    <div class="code-block">
// 计算整个集合的总包围盒
const totalBoundingBox = new Box3();
boundingBoxes.forEach(box => {
    totalBoundingBox.union(box);
});
// 获取总包围盒的真实中心
const center = new Vector3();
totalBoundingBox.getCenter(center);
                    </div>
                    <p><span class="success">优势</span>：基于真实几何形状计算中心</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🚀 性能优化</h3>
            <p>新的实现还包含了多项性能优化：</p>
            <div class="code-block">
// 智能缓存机制
if (!parentMesh.userData.cachedCollectionData || 
    this.shouldRecalculateCollectionCenter(parentMesh)) {
    this.calculateAndCacheCollectionData(parentMesh);
}

// 自动检测变化
shouldRecalculateCollectionCenter(parentMesh) {
    // 检查缓存时间和子对象数量变化
    return timeExpired || meshCountChanged;
}
            </div>
        </div>
        
        <div class="section">
            <h3>📊 使用方法</h3>
            <p>修复后的代码会自动使用新的计算方法，无需修改调用代码：</p>
            <div class="code-block">
// 原有调用方式保持不变
routeSimulator.rotateMeshByName('wheelAssembly', 45);

// 新增的调试和管理功能
routeSimulator.setDebugMode(true);  // 查看详细计算过程
routeSimulator.clearMeshRotationCache(); // 清理缓存
const stats = routeSimulator.getRotationPerformanceStats(); // 获取统计
            </div>
        </div>
        
        <div class="section">
            <h3>🎯 预期效果</h3>
            <ul class="solution-list">
                <li><strong>旋转中心更准确</strong>：子模型集合会围绕真实的几何中心旋转</li>
                <li><strong>视觉效果更自然</strong>：旋转动画看起来更符合物理直觉</li>
                <li><strong>性能显著提升</strong>：通过缓存机制减少重复计算</li>
                <li><strong>向后兼容</strong>：不影响现有代码的使用方式</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="showTestResult()">查看修复效果示例</button>
        
        <div id="testResult" style="display: none;">
            <div class="section">
                <h3>📈 修复效果示例</h3>
                <p>假设有一个由3个不同大小的子mesh组成的轮子集合：</p>
                <div class="code-block">
子mesh1: 位置(-10, 0, -10), 大小(10x4x10) - 大轮毂
子mesh2: 位置(10, 0, 10),   大小(6x2x6)   - 小轮毂  
子mesh3: 位置(0, 5, 0),    大小(4x2x4)   - 轮轴

修复前中心点: (0, 1.67, 0)     - 简单平均，偏向小部件
修复后中心点: (-1.2, 0.8, -1.2) - 真实几何中心，更合理

旋转效果: 修复后的旋转看起来更自然，大轮毂的影响更大
                </div>
                <p class="success">✅ 修复完成！子模型集合现在会围绕正确的几何中心旋转。</p>
            </div>
        </div>
    </div>

    <script>
        function showTestResult() {
            const testResult = document.getElementById('testResult');
            if (testResult.style.display === 'none') {
                testResult.style.display = 'block';
                event.target.textContent = '隐藏修复效果示例';
            } else {
                testResult.style.display = 'none';
                event.target.textContent = '查看修复效果示例';
            }
        }
    </script>
</body>
</html>
