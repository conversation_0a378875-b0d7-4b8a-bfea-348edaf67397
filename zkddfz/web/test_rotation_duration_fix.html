<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试旋转Duration修复</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        input[type="number"] {
            width: 100px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .scenario-btn {
            background: #28a745;
            padding: 10px;
            text-align: center;
            font-size: 12px;
        }
        
        .scenario-btn:hover {
            background: #218838;
        }
        
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-line;
            max-height: 400px;
            overflow-y: auto;
        }
        
        #canvas-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试旋转Duration修复</h1>
        
        <div class="status">
            <strong>测试目标：</strong>验证旋转动画是否根据电文中的duration正确控制速度，避免旋转过快或旋转多圈
        </div>
        
        <div class="controls">
            <h3>手动测试</h3>
            
            <div class="control-group">
                <label>目标角度:</label>
                <input type="number" id="targetAngle" value="90" min="0" max="360" step="1">°
            </div>
            
            <div class="control-group">
                <label>动画时长:</label>
                <input type="number" id="duration" value="2000" min="10" max="10000" step="10">ms
            </div>
            
            <div class="control-group">
                <label>当前角度:</label>
                <span id="currentAngleDisplay">0°</span>
                <label style="margin-left: 20px;">旋转速度:</label>
                <span id="rotationSpeedDisplay">0°/s</span>
            </div>
            
            <div class="control-group">
                <button onclick="testRotation()">开始旋转</button>
                <button onclick="resetMesh()">重置</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <h4>预设测试场景</h4>
            <div class="test-scenarios">
                <button class="scenario-btn" onclick="testScenario(90, 2000)">
                    正常旋转<br>90° / 2s
                </button>
                <button class="scenario-btn" onclick="testScenario(180, 50)">
                    极短时间<br>180° / 50ms
                </button>
                <button class="scenario-btn" onclick="testScenario(720, 3000)">
                    大角度<br>720° / 3s
                </button>
                <button class="scenario-btn" onclick="testScenario(350, 1000)">
                    跨越边界<br>350° / 1s
                </button>
                <button class="scenario-btn" onclick="testScenario(45, 5000)">
                    慢速旋转<br>45° / 5s
                </button>
                <button class="scenario-btn" onclick="testScenario(270, 10)">
                    超短时间<br>270° / 10ms
                </button>
                <button class="scenario-btn" onclick="testSequentialRotation()">
                    连续旋转测试
                </button>
                <button class="scenario-btn" onclick="testRandomAngles()">
                    随机角度测试
                </button>
            </div>
            
            <div class="info" id="logInfo">
等待测试开始...
            </div>
        </div>
        
        <div id="canvas-container">
            <canvas id="testCanvas" width="800" height="600"></canvas>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.155.0/build/three.module.js';
        
        // 全局变量
        let scene, camera, renderer, testMesh, routeSimulator;
        let targetAngleInput, durationInput, currentAngleDisplay, rotationSpeedDisplay, logInfo;
        
        // 缓动函数
        const EasingFunctions = {
            linear: (t) => t,
            easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        };
        
        // 模拟修复后的 RouteSimulator
        class TestRouteSimulator {
            constructor() {
                this.model = null;
                this.isRunning = true;
                this.isPaused = false;
                this.options = { smoothRotation: true, speed: 1.0 };
            }
            
            setModel(model) {
                this.model = model;
            }
            
            // 标准化角度到 0-360 度范围
            normalizeAngle(angle) {
                while (angle >= 360) angle -= 360;
                while (angle < 0) angle += 360;
                return angle;
            }
            
            // 计算最短旋转路径
            calculateShortestRotationPath(startAngle, targetAngle) {
                const normalizedStart = this.normalizeAngle(startAngle);
                const normalizedTarget = this.normalizeAngle(targetAngle);
                
                let diff = normalizedTarget - normalizedStart;
                
                if (diff > 180) {
                    diff -= 360;
                } else if (diff < -180) {
                    diff += 360;
                }
                
                return diff;
            }
            
            // 验证和修正旋转数据
            validateAndCorrectRotationData(rotationData, duration) {
                const corrected = {
                    rotation: { ...rotationData },
                    duration: duration
                };

                // 标准化角度值
                if (corrected.rotation.y !== undefined) {
                    corrected.rotation.y = this.normalizeAngle(corrected.rotation.y);
                }

                // 确保duration不会太小
                const minDuration = 100;
                if (corrected.duration < minDuration) {
                    log(`⚠️ 动画时长过短 (${duration}ms)，调整为最小值 ${minDuration}ms`);
                    corrected.duration = minDuration;
                }

                return corrected;
            }
            
            // 获取mesh当前的Y轴旋转角度
            getMeshCurrentRotation(mesh) {
                if (!mesh) return 0;

                let currentAngle = 0;

                if (mesh.userData && mesh.userData.currentRotationY !== undefined) {
                    currentAngle = mesh.userData.currentRotationY;
                } else {
                    currentAngle = THREE.MathUtils.radToDeg(mesh.rotation.y);
                }

                return this.normalizeAngle(currentAngle);
            }
            
            // 旋转mesh到指定角度
            rotateMeshAroundCenter(mesh, yRotationDegrees) {
                if (!mesh) return;

                mesh.rotation.y = THREE.MathUtils.degToRad(yRotationDegrees);
                mesh.userData.currentRotationY = yRotationDegrees;
                
                updateCurrentAngleDisplay(yRotationDegrees);
            }
            
            // 模拟电文处理
            simulateMessageProcessing(targetAngle, duration) {
                const point = {
                    rotation: { y: targetAngle },
                    duration: duration
                };
                
                // 验证和修正旋转数据
                const correctedData = this.validateAndCorrectRotationData(point.rotation, duration);
                
                log(`📦 电文数据验证:`);
                log(`- 原始duration: ${point.duration}ms, 修正后: ${correctedData.duration}ms`);
                log(`- 原始旋转: y=${point.rotation.y}°, 修正后: y=${correctedData.rotation.y}°`);
                
                // 调用旋转动画
                this.animateMeshRotation('testMesh', correctedData.rotation.y, correctedData.duration);
            }
            
            // 动画旋转mesh
            animateMeshRotation(meshName, yRotationDegrees, duration, onComplete) {
                if (!this.model) {
                    console.warn('Model not available for mesh rotation animation');
                    if (onComplete) onComplete();
                    return;
                }

                const targetMesh = this.model;

                // 确保输入的角度是标准化的
                const inputAngleNormalized = this.normalizeAngle(yRotationDegrees);
                
                // 获取当前旋转角度并标准化
                const currentRotationDegrees = this.normalizeAngle(this.getMeshCurrentRotation(targetMesh));
                
                // 计算最短旋转路径
                const rotationDiff = this.calculateShortestRotationPath(currentRotationDegrees, inputAngleNormalized);
                
                // 检查duration是否合理
                const minDuration = 100;
                const actualDuration = Math.max(duration, minDuration);
                
                // 计算旋转速度
                const rotationSpeed = Math.abs(rotationDiff) / (actualDuration / 1000);
                
                const startTime = performance.now();

                log(`🔄 开始动画旋转mesh: ${meshName}`);
                log(`📐 当前角度: ${currentRotationDegrees.toFixed(2)}°`);
                log(`🎯 目标角度: ${inputAngleNormalized.toFixed(2)}° (原始: ${yRotationDegrees.toFixed(2)}°)`);
                log(`📏 旋转差值: ${rotationDiff.toFixed(2)}°`);
                log(`⏱️ 动画时长: ${actualDuration}ms (原始: ${duration}ms)`);
                log(`🚀 旋转速度: ${rotationSpeed.toFixed(2)}°/s`);
                
                // 更新速度显示
                updateRotationSpeedDisplay(rotationSpeed);
                
                if (rotationSpeed > 360) {
                    log(`⚠️ 旋转速度过快: ${rotationSpeed.toFixed(2)}°/s，可能导致视觉效果不佳`);
                }
                
                // 如果角度差值很小，直接设置
                if (Math.abs(rotationDiff) < 0.1) {
                    log(`角度差值很小 (${rotationDiff.toFixed(2)}°)，直接设置最终角度`);
                    this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
                    if (onComplete) onComplete();
                    return;
                }

                const animate = (currentTime) => {
                    if (!this.isRunning || this.isPaused) return;

                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / actualDuration, 1);

                    const easing = this.options.smoothRotation
                        ? EasingFunctions.easeInOutQuad(progress)
                        : progress;

                    const currentAngle = currentRotationDegrees + rotationDiff * easing;

                    this.rotateMeshAroundCenter(targetMesh, currentAngle);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
                        log(`✅ mesh旋转动画完成: ${meshName}, 最终角度: ${inputAngleNormalized.toFixed(2)}°`);
                        updateRotationSpeedDisplay(0);
                        if (onComplete) onComplete();
                    }
                };

                requestAnimationFrame(animate);
            }
        }
        
        // 初始化场景
        function initScene() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            const canvas = document.getElementById('testCanvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(800, 600);
            
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // 创建箭头形状的测试mesh
            const geometry = new THREE.ConeGeometry(0.5, 2, 8);
            const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            testMesh = new THREE.Mesh(geometry, material);
            testMesh.name = 'testMesh';
            testMesh.position.set(0, 1, 0);
            testMesh.userData.currentRotationY = 0;
            scene.add(testMesh);
            
            const axesHelper = new THREE.AxesHelper(3);
            scene.add(axesHelper);
            
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            routeSimulator = new TestRouteSimulator();
            routeSimulator.setModel(testMesh);
            
            animate();
            
            log('场景初始化完成');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logInfo.textContent += `[${timestamp}] ${message}\n`;
            logInfo.scrollTop = logInfo.scrollHeight;
        }
        
        function updateCurrentAngleDisplay(angle) {
            currentAngleDisplay.textContent = angle.toFixed(1) + '°';
        }
        
        function updateRotationSpeedDisplay(speed) {
            rotationSpeedDisplay.textContent = speed.toFixed(1) + '°/s';
        }
        
        window.testRotation = function() {
            const targetAngle = parseFloat(targetAngleInput.value);
            const duration = parseInt(durationInput.value);
            
            log(`\n=== 开始手动测试 ===`);
            log(`目标角度: ${targetAngle}°, 时长: ${duration}ms`);
            routeSimulator.simulateMessageProcessing(targetAngle, duration);
        }
        
        window.testScenario = function(angle, duration) {
            targetAngleInput.value = angle;
            durationInput.value = duration;
            
            log(`\n=== 预设场景测试 ===`);
            log(`场景: ${angle}° / ${duration}ms`);
            routeSimulator.simulateMessageProcessing(angle, duration);
        }
        
        window.testSequentialRotation = function() {
            log(`\n=== 连续旋转测试 ===`);
            const sequence = [
                { angle: 45, duration: 1000 },
                { angle: 135, duration: 500 },
                { angle: 225, duration: 2000 },
                { angle: 315, duration: 100 },
                { angle: 0, duration: 1500 }
            ];
            
            let index = 0;
            function nextRotation() {
                if (index < sequence.length) {
                    const test = sequence[index];
                    log(`连续测试 ${index + 1}/${sequence.length}: ${test.angle}° / ${test.duration}ms`);
                    routeSimulator.simulateMessageProcessing(test.angle, test.duration);
                    index++;
                    setTimeout(nextRotation, test.duration + 500);
                } else {
                    log('连续旋转测试完成');
                }
            }
            nextRotation();
        }
        
        window.testRandomAngles = function() {
            log(`\n=== 随机角度测试 ===`);
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const randomAngle = Math.random() * 720; // 0-720度
                    const randomDuration = Math.random() * 3000 + 100; // 100-3100ms
                    log(`随机测试 ${i + 1}: ${randomAngle.toFixed(1)}° / ${randomDuration.toFixed(0)}ms`);
                    routeSimulator.simulateMessageProcessing(randomAngle, randomDuration);
                }, i * 4000);
            }
        }
        
        window.resetMesh = function() {
            routeSimulator.rotateMeshAroundCenter(testMesh, 0);
            updateRotationSpeedDisplay(0);
            log('mesh已重置到0°');
        }
        
        window.clearLog = function() {
            logInfo.textContent = '';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            targetAngleInput = document.getElementById('targetAngle');
            durationInput = document.getElementById('duration');
            currentAngleDisplay = document.getElementById('currentAngleDisplay');
            rotationSpeedDisplay = document.getElementById('rotationSpeedDisplay');
            logInfo = document.getElementById('logInfo');
            
            initScene();
        });
    </script>
</body>
</html>
