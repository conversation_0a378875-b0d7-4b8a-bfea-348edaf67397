<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试旋转速度修复</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        
        input[type="number"] {
            width: 80px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-line;
            max-height: 300px;
            overflow-y: auto;
        }
        
        #canvas-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试旋转速度修复</h1>
        
        <div class="controls">
            <h3>旋转控制</h3>
            
            <div class="control-group">
                <label>目标角度:</label>
                <input type="number" id="targetAngle" value="90" min="0" max="360" step="1">°
                <button onclick="animateToAngle()">动画旋转</button>
            </div>
            
            <div class="control-group">
                <label>动画时长:</label>
                <input type="number" id="duration" value="2000" min="100" max="10000" step="100">ms
            </div>
            
            <div class="control-group">
                <label>当前角度:</label>
                <span id="currentAngleDisplay">0°</span>
            </div>
            
            <div class="test-buttons">
                <button onclick="testAngle(45)">45°</button>
                <button onclick="testAngle(90)">90°</button>
                <button onclick="testAngle(180)">180°</button>
                <button onclick="testAngle(270)">270°</button>
                <button onclick="testAngle(360)">360°</button>
                <button onclick="testAngle(0)">0°</button>
                <button onclick="testSequence()">测试序列</button>
                <button onclick="testCrossover()">跨越测试</button>
                <button onclick="resetMesh()">重置</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="info" id="logInfo">
等待测试开始...
            </div>
        </div>
        
        <div id="canvas-container">
            <canvas id="testCanvas" width="800" height="600"></canvas>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.155.0/build/three.module.js';
        
        // 全局变量
        let scene, camera, renderer, testMesh, routeSimulator;
        let targetAngleInput, durationInput, currentAngleDisplay, logInfo;
        
        // 缓动函数
        const EasingFunctions = {
            linear: (t) => t,
            easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        };
        
        // 模拟 RouteSimulator 的旋转方法
        class TestRouteSimulator {
            constructor() {
                this.model = null;
                this.isRunning = true;
                this.isPaused = false;
                this.options = { smoothRotation: true };
            }
            
            setModel(model) {
                this.model = model;
            }
            
            // 标准化角度到 0-360 度范围
            normalizeAngle(angle) {
                while (angle >= 360) angle -= 360;
                while (angle < 0) angle += 360;
                return angle;
            }
            
            // 计算最短旋转路径
            calculateShortestRotationPath(startAngle, targetAngle) {
                const normalizedStart = this.normalizeAngle(startAngle);
                const normalizedTarget = this.normalizeAngle(targetAngle);
                
                let diff = normalizedTarget - normalizedStart;
                
                if (diff > 180) {
                    diff -= 360; // 逆时针更短
                } else if (diff < -180) {
                    diff += 360; // 顺时针更短
                }
                
                return diff;
            }
            
            // 获取mesh当前的Y轴旋转角度
            getMeshCurrentRotation(mesh) {
                if (!mesh) return 0;

                let currentAngle = 0;

                if (mesh.userData && mesh.userData.currentRotationY !== undefined) {
                    currentAngle = mesh.userData.currentRotationY;
                } else {
                    currentAngle = THREE.MathUtils.radToDeg(mesh.rotation.y);
                }

                return this.normalizeAngle(currentAngle);
            }
            
            // 旋转mesh到指定角度
            rotateMeshAroundCenter(mesh, yRotationDegrees) {
                if (!mesh) return;

                // 简化版本：直接设置旋转
                mesh.rotation.y = THREE.MathUtils.degToRad(yRotationDegrees);
                mesh.userData.currentRotationY = yRotationDegrees;
                
                // 更新显示
                updateCurrentAngleDisplay(yRotationDegrees);
            }
            
            // 动画旋转mesh
            animateMeshRotation(meshName, yRotationDegrees, duration, onComplete) {
                if (!this.model) {
                    console.warn('Model not available for mesh rotation animation');
                    if (onComplete) onComplete();
                    return;
                }

                const targetMesh = this.model;

                // 获取当前旋转角度并标准化
                const currentRotationDegrees = this.normalizeAngle(this.getMeshCurrentRotation(targetMesh));
                const targetRotationNormalized = this.normalizeAngle(yRotationDegrees);
                
                // 计算最短旋转路径
                const rotationDiff = this.calculateShortestRotationPath(currentRotationDegrees, targetRotationNormalized);
                const finalTargetRotation = currentRotationDegrees + rotationDiff;
                
                const startTime = performance.now();

                log(`开始动画旋转mesh: ${meshName}`);
                log(`- 当前角度: ${currentRotationDegrees.toFixed(2)}°`);
                log(`- 目标角度: ${targetRotationNormalized.toFixed(2)}°`);
                log(`- 旋转差值: ${rotationDiff.toFixed(2)}°`);
                log(`- 最终目标: ${finalTargetRotation.toFixed(2)}°`);
                log(`- 动画时长: ${duration}ms`);

                const animate = (currentTime) => {
                    if (!this.isRunning || this.isPaused) return;

                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // 使用平滑缓动
                    const easing = this.options.smoothRotation
                        ? EasingFunctions.easeInOutQuad(progress)
                        : progress;

                    // 线性插值计算当前角度
                    const currentAngle = currentRotationDegrees + rotationDiff * easing;

                    // 旋转mesh
                    this.rotateMeshAroundCenter(targetMesh, currentAngle);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 确保最终角度精确
                        this.rotateMeshAroundCenter(targetMesh, targetRotationNormalized);
                        log(`mesh旋转动画完成: ${meshName}, 最终角度: ${targetRotationNormalized.toFixed(2)}°`);
                        if (onComplete) onComplete();
                    }
                };

                requestAnimationFrame(animate);
            }
            
            findMeshByName(object, name) {
                return object; // 简化版本
            }
        }
        
        // 初始化场景
        function initScene() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            const canvas = document.getElementById('testCanvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(800, 600);
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // 创建测试mesh（一个箭头形状，便于观察旋转方向）
            const geometry = new THREE.ConeGeometry(0.5, 2, 8);
            const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            testMesh = new THREE.Mesh(geometry, material);
            testMesh.name = 'testMesh';
            testMesh.position.set(0, 1, 0);
            testMesh.userData.currentRotationY = 0;
            scene.add(testMesh);
            
            // 添加坐标轴辅助器
            const axesHelper = new THREE.AxesHelper(3);
            scene.add(axesHelper);
            
            // 添加网格
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            // 创建RouteSimulator实例
            routeSimulator = new TestRouteSimulator();
            routeSimulator.setModel(testMesh);
            
            // 开始渲染循环
            animate();
            
            log('场景初始化完成');
            log(`测试mesh初始位置: (${testMesh.position.x}, ${testMesh.position.y}, ${testMesh.position.z})`);
        }
        
        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        
        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logInfo.textContent += `[${timestamp}] ${message}\n`;
            logInfo.scrollTop = logInfo.scrollHeight;
        }
        
        // 更新当前角度显示
        function updateCurrentAngleDisplay(angle) {
            currentAngleDisplay.textContent = angle.toFixed(1) + '°';
        }
        
        // 动画旋转到指定角度
        window.animateToAngle = function() {
            const targetAngle = parseFloat(targetAngleInput.value);
            const duration = parseInt(durationInput.value);
            
            log(`开始旋转到 ${targetAngle}°，时长 ${duration}ms`);
            routeSimulator.animateMeshRotation('testMesh', targetAngle, duration);
        }
        
        // 测试指定角度
        window.testAngle = function(angle) {
            targetAngleInput.value = angle;
            animateToAngle();
        }
        
        // 测试序列
        window.testSequence = function() {
            log('开始序列测试...');
            const angles = [45, 90, 135, 180, 225, 270, 315, 0];
            let index = 0;
            
            function nextAngle() {
                if (index < angles.length) {
                    const angle = angles[index];
                    log(`序列测试 ${index + 1}/${angles.length}: ${angle}°`);
                    routeSimulator.animateMeshRotation('testMesh', angle, 1500, () => {
                        index++;
                        setTimeout(nextAngle, 500);
                    });
                } else {
                    log('序列测试完成');
                }
            }
            
            nextAngle();
        }
        
        // 跨越测试（测试跨越0°/360°边界）
        window.testCrossover = function() {
            log('开始跨越测试...');
            const sequence = [
                { from: 10, to: 350 },
                { from: 350, to: 10 },
                { from: 5, to: 355 },
                { from: 355, to: 5 }
            ];
            
            let index = 0;
            
            function nextTest() {
                if (index < sequence.length) {
                    const test = sequence[index];
                    log(`跨越测试 ${index + 1}: ${test.from}° -> ${test.to}°`);
                    
                    // 先设置起始角度
                    routeSimulator.rotateMeshAroundCenter(testMesh, test.from);
                    
                    setTimeout(() => {
                        routeSimulator.animateMeshRotation('testMesh', test.to, 2000, () => {
                            index++;
                            setTimeout(nextTest, 1000);
                        });
                    }, 500);
                } else {
                    log('跨越测试完成');
                }
            }
            
            nextTest();
        }
        
        // 重置mesh
        window.resetMesh = function() {
            routeSimulator.rotateMeshAroundCenter(testMesh, 0);
            log('mesh已重置到0°');
        }
        
        // 清空日志
        window.clearLog = function() {
            logInfo.textContent = '';
        }
        
        // 初始化控件
        document.addEventListener('DOMContentLoaded', function() {
            targetAngleInput = document.getElementById('targetAngle');
            durationInput = document.getElementById('duration');
            currentAngleDisplay = document.getElementById('currentAngleDisplay');
            logInfo = document.getElementById('logInfo');
            
            // 初始化场景
            initScene();
        });
    </script>
</body>
</html>
