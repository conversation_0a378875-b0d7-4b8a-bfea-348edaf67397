<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试角度单位修复</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        
        input[type="number"] {
            width: 100px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .test-btn {
            background: #28a745;
            padding: 10px;
            text-align: center;
            font-size: 12px;
        }
        
        .test-btn:hover {
            background: #218838;
        }
        
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-line;
            max-height: 400px;
            overflow-y: auto;
        }
        
        #canvas-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .angle-display {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试角度单位修复</h1>
        
        <div class="status">
            <strong>测试重点：</strong>验证传入的角度值（度制）是否被正确处理，避免因为角度单位混乱导致的旋转过快或多圈旋转问题
        </div>
        
        <div class="angle-display" id="angleDisplay">
            当前角度: 0° (0 弧度) | 目标角度: 0° (0 弧度) | 旋转差值: 0° (0 弧度)
        </div>
        
        <div class="controls">
            <h3>角度单位测试</h3>
            
            <div class="control-group">
                <label>目标角度(度):</label>
                <input type="number" id="targetAngle" value="90" min="-720" max="720" step="1">°
                <button onclick="testAngleRotation()">测试旋转</button>
            </div>
            
            <div class="control-group">
                <label>动画时长:</label>
                <input type="number" id="duration" value="2000" min="100" max="5000" step="100">ms
            </div>
            
            <div class="control-group">
                <button onclick="resetMesh()">重置到0°</button>
                <button onclick="getCurrentAngle()">获取当前角度</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <h4>角度单位测试场景</h4>
            <div class="test-grid">
                <button class="test-btn" onclick="testAngle(45)">45°</button>
                <button class="test-btn" onclick="testAngle(90)">90°</button>
                <button class="test-btn" onclick="testAngle(180)">180°</button>
                <button class="test-btn" onclick="testAngle(270)">270°</button>
                <button class="test-btn" onclick="testAngle(360)">360°</button>
                <button class="test-btn" onclick="testAngle(-90)">-90°</button>
                <button class="test-btn" onclick="testAngle(450)">450°</button>
                <button class="test-btn" onclick="testAngle(720)">720°</button>
                <button class="test-btn" onclick="testAngle(-180)">-180°</button>
                <button class="test-btn" onclick="testAngle(0)">0°</button>
                <button class="test-btn" onclick="testSequence()">序列测试</button>
                <button class="test-btn" onclick="testBoundary()">边界测试</button>
            </div>
            
            <div class="info" id="logInfo">
等待测试开始...
            </div>
        </div>
        
        <div id="canvas-container">
            <canvas id="testCanvas" width="800" height="600"></canvas>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.155.0/build/three.module.js';
        
        // 全局变量
        let scene, camera, renderer, testMesh, routeSimulator;
        let targetAngleInput, durationInput, logInfo, angleDisplay;
        
        // 缓动函数
        const EasingFunctions = {
            linear: (t) => t,
            easeInOutQuad: (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        };
        
        // 模拟修复后的 RouteSimulator
        class TestRouteSimulator {
            constructor() {
                this.model = null;
                this.isRunning = true;
                this.isPaused = false;
                this.options = { smoothRotation: true, speed: 1.0 };
            }
            
            setModel(model) {
                this.model = model;
            }
            
            // 标准化角度到 0-360 度范围
            normalizeAngle(angle) {
                while (angle >= 360) angle -= 360;
                while (angle < 0) angle += 360;
                return angle;
            }
            
            // 计算最短旋转路径
            calculateShortestRotationPath(startAngle, targetAngle) {
                const normalizedStart = this.normalizeAngle(startAngle);
                const normalizedTarget = this.normalizeAngle(targetAngle);
                
                let diff = normalizedTarget - normalizedStart;
                
                if (diff > 180) {
                    diff -= 360;
                } else if (diff < -180) {
                    diff += 360;
                }
                
                return diff;
            }
            
            // 获取mesh当前的Y轴旋转角度
            getMeshCurrentRotation(mesh) {
                if (!mesh) return 0;

                let currentAngle = 0;

                if (mesh.userData && mesh.userData.currentRotationY !== undefined) {
                    currentAngle = mesh.userData.currentRotationY;
                } else {
                    currentAngle = THREE.MathUtils.radToDeg(mesh.rotation.y);
                }

                return this.normalizeAngle(currentAngle);
            }
            
            // 初始化mesh旋转数据
            initializeMeshRotationData(mesh) {
                if (!mesh) return;

                mesh.updateMatrixWorld(true);

                // 简化的中心点计算
                const center = mesh.position.clone();

                mesh.userData.rotationData = {
                    center: center.clone(),
                    originalPosition: mesh.position.clone(),
                    originalRotation: mesh.rotation.clone(),
                    originalScale: mesh.scale.clone()
                };

                mesh.userData.currentRotationY = THREE.MathUtils.radToDeg(mesh.rotation.y);

                log(`初始化mesh旋转数据:`);
                log(`- 中心点: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);
                log(`- 原始旋转(弧度): ${mesh.rotation.y.toFixed(4)}`);
                log(`- 原始旋转(角度): ${mesh.userData.currentRotationY.toFixed(2)}°`);
            }
            
            // 围绕中心点旋转
            applyRotationAroundPoint(mesh, centerPoint, targetRotationRad) {
                if (!mesh || !centerPoint) return;

                const rotationData = mesh.userData.rotationData;
                if (!rotationData) return;

                // 重置到原始状态
                mesh.position.copy(rotationData.originalPosition);
                mesh.rotation.copy(rotationData.originalRotation);
                mesh.scale.copy(rotationData.originalScale);

                const rotationCenter = rotationData.center.clone();
                const rotationDelta = targetRotationRad - rotationData.originalRotation.y;

                log(`🔧 计算旋转变换:`);
                log(`- 原始旋转(弧度): ${rotationData.originalRotation.y.toFixed(4)} (${THREE.MathUtils.radToDeg(rotationData.originalRotation.y).toFixed(2)}°)`);
                log(`- 目标旋转(弧度): ${targetRotationRad.toFixed(4)} (${THREE.MathUtils.radToDeg(targetRotationRad).toFixed(2)}°)`);
                log(`- 旋转增量(弧度): ${rotationDelta.toFixed(4)} (${THREE.MathUtils.radToDeg(rotationDelta).toFixed(2)}°)`);

                // 计算相对位置并应用旋转
                const relativePosition = rotationData.originalPosition.clone().sub(rotationCenter);
                const rotationMatrix = new THREE.Matrix4().makeRotationY(rotationDelta);
                relativePosition.applyMatrix4(rotationMatrix);

                // 设置新位置和旋转
                mesh.position.copy(rotationCenter).add(relativePosition);
                mesh.rotation.y = targetRotationRad;

                log(`✅ 旋转变换完成: 最终旋转(弧度): ${mesh.rotation.y.toFixed(4)} (${THREE.MathUtils.radToDeg(mesh.rotation.y).toFixed(2)}°)`);
            }
            
            // 旋转mesh到指定角度
            rotateMeshAroundCenter(mesh, yRotationDegrees) {
                if (!mesh) return;

                if (!mesh.userData.rotationData) {
                    this.initializeMeshRotationData(mesh);
                }

                const rotationData = mesh.userData.rotationData;
                const normalizedDegrees = this.normalizeAngle(yRotationDegrees);
                const targetRotationRad = THREE.MathUtils.degToRad(normalizedDegrees);

                log(`🔧 旋转mesh "${mesh.name}":`);
                log(`- 输入角度: ${yRotationDegrees.toFixed(2)}°`);
                log(`- 标准化角度: ${normalizedDegrees.toFixed(2)}°`);
                log(`- 目标弧度: ${targetRotationRad.toFixed(4)}`);

                this.applyRotationAroundPoint(mesh, rotationData.center, targetRotationRad);

                mesh.userData.currentRotationY = normalizedDegrees;
                updateAngleDisplay();

                log(`✅ mesh旋转完成: ${normalizedDegrees.toFixed(2)}°`);
            }
            
            // 动画旋转mesh
            animateMeshRotation(meshName, yRotationDegrees, duration, onComplete) {
                if (!this.model) {
                    if (onComplete) onComplete();
                    return;
                }

                const targetMesh = this.model;
                const inputAngleNormalized = this.normalizeAngle(yRotationDegrees);
                const currentRotationDegrees = this.normalizeAngle(this.getMeshCurrentRotation(targetMesh));
                const rotationDiff = this.calculateShortestRotationPath(currentRotationDegrees, inputAngleNormalized);
                const actualDuration = Math.max(duration, 100);
                const rotationSpeed = Math.abs(rotationDiff) / (actualDuration / 1000);
                
                const startTime = performance.now();

                log(`🔄 开始动画旋转:`);
                log(`- 当前角度: ${currentRotationDegrees.toFixed(2)}°`);
                log(`- 目标角度: ${inputAngleNormalized.toFixed(2)}° (原始: ${yRotationDegrees.toFixed(2)}°)`);
                log(`- 旋转差值: ${rotationDiff.toFixed(2)}°`);
                log(`- 动画时长: ${actualDuration}ms`);
                log(`- 旋转速度: ${rotationSpeed.toFixed(2)}°/s`);
                
                if (Math.abs(rotationDiff) < 0.1) {
                    this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
                    if (onComplete) onComplete();
                    return;
                }

                const animate = (currentTime) => {
                    if (!this.isRunning || this.isPaused) return;

                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / actualDuration, 1);
                    const easing = this.options.smoothRotation ? EasingFunctions.easeInOutQuad(progress) : progress;
                    const currentAngle = currentRotationDegrees + rotationDiff * easing;

                    this.rotateMeshAroundCenter(targetMesh, currentAngle);

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        this.rotateMeshAroundCenter(targetMesh, inputAngleNormalized);
                        log(`✅ 动画旋转完成: ${inputAngleNormalized.toFixed(2)}°`);
                        if (onComplete) onComplete();
                    }
                };

                requestAnimationFrame(animate);
            }
        }
        
        // 初始化场景
        function initScene() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            const canvas = document.getElementById('testCanvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(800, 600);
            
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // 创建箭头形状的测试mesh
            const geometry = new THREE.ConeGeometry(0.5, 2, 8);
            const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            testMesh = new THREE.Mesh(geometry, material);
            testMesh.name = 'testMesh';
            testMesh.position.set(0, 1, 0);
            testMesh.userData.currentRotationY = 0;
            scene.add(testMesh);
            
            const axesHelper = new THREE.AxesHelper(3);
            scene.add(axesHelper);
            
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            routeSimulator = new TestRouteSimulator();
            routeSimulator.setModel(testMesh);
            
            animate();
            updateAngleDisplay();
            
            log('场景初始化完成');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logInfo.textContent += `[${timestamp}] ${message}\n`;
            logInfo.scrollTop = logInfo.scrollHeight;
        }
        
        function updateAngleDisplay() {
            const currentAngleDeg = routeSimulator.getMeshCurrentRotation(testMesh);
            const currentAngleRad = THREE.MathUtils.degToRad(currentAngleDeg);
            const targetAngleDeg = parseFloat(targetAngleInput.value);
            const targetAngleRad = THREE.MathUtils.degToRad(targetAngleDeg);
            const diffDeg = routeSimulator.calculateShortestRotationPath(currentAngleDeg, targetAngleDeg);
            const diffRad = THREE.MathUtils.degToRad(diffDeg);
            
            angleDisplay.textContent = 
                `当前角度: ${currentAngleDeg.toFixed(1)}° (${currentAngleRad.toFixed(4)} 弧度) | ` +
                `目标角度: ${targetAngleDeg.toFixed(1)}° (${targetAngleRad.toFixed(4)} 弧度) | ` +
                `旋转差值: ${diffDeg.toFixed(1)}° (${diffRad.toFixed(4)} 弧度)`;
        }
        
        window.testAngleRotation = function() {
            const targetAngle = parseFloat(targetAngleInput.value);
            const duration = parseInt(durationInput.value);
            
            log(`\n=== 角度单位测试 ===`);
            log(`输入角度: ${targetAngle}° (${THREE.MathUtils.degToRad(targetAngle).toFixed(4)} 弧度)`);
            log(`动画时长: ${duration}ms`);
            
            updateAngleDisplay();
            routeSimulator.animateMeshRotation('testMesh', targetAngle, duration);
        }
        
        window.testAngle = function(angle) {
            targetAngleInput.value = angle;
            updateAngleDisplay();
            testAngleRotation();
        }
        
        window.testSequence = function() {
            log(`\n=== 角度序列测试 ===`);
            const angles = [0, 45, 90, 180, 270, 360, 450, -90, -180, 0];
            let index = 0;
            
            function nextTest() {
                if (index < angles.length) {
                    const angle = angles[index];
                    log(`序列测试 ${index + 1}/${angles.length}: ${angle}°`);
                    targetAngleInput.value = angle;
                    updateAngleDisplay();
                    routeSimulator.animateMeshRotation('testMesh', angle, 1500, () => {
                        index++;
                        setTimeout(nextTest, 500);
                    });
                } else {
                    log('序列测试完成');
                }
            }
            nextTest();
        }
        
        window.testBoundary = function() {
            log(`\n=== 边界测试 ===`);
            const tests = [
                { from: 10, to: 350 },
                { from: 350, to: 10 },
                { from: 0, to: 359 },
                { from: 359, to: 1 }
            ];
            
            let index = 0;
            function nextBoundaryTest() {
                if (index < tests.length) {
                    const test = tests[index];
                    log(`边界测试 ${index + 1}: ${test.from}° -> ${test.to}°`);
                    
                    routeSimulator.rotateMeshAroundCenter(testMesh, test.from);
                    updateAngleDisplay();
                    
                    setTimeout(() => {
                        targetAngleInput.value = test.to;
                        updateAngleDisplay();
                        routeSimulator.animateMeshRotation('testMesh', test.to, 2000, () => {
                            index++;
                            setTimeout(nextBoundaryTest, 1000);
                        });
                    }, 500);
                } else {
                    log('边界测试完成');
                }
            }
            nextBoundaryTest();
        }
        
        window.resetMesh = function() {
            routeSimulator.rotateMeshAroundCenter(testMesh, 0);
            targetAngleInput.value = 0;
            updateAngleDisplay();
            log('mesh已重置到0°');
        }
        
        window.getCurrentAngle = function() {
            const currentAngle = routeSimulator.getMeshCurrentRotation(testMesh);
            const currentRad = testMesh.rotation.y;
            log(`当前角度: ${currentAngle.toFixed(2)}° (${currentRad.toFixed(4)} 弧度)`);
            updateAngleDisplay();
        }
        
        window.clearLog = function() {
            logInfo.textContent = '';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            targetAngleInput = document.getElementById('targetAngle');
            durationInput = document.getElementById('duration');
            logInfo = document.getElementById('logInfo');
            angleDisplay = document.getElementById('angleDisplay');
            
            targetAngleInput.addEventListener('input', updateAngleDisplay);
            
            initScene();
        });
    </script>
</body>
</html>
