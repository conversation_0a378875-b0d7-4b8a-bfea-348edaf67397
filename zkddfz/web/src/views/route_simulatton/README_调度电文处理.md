# 调度电文处理功能说明

## 功能概述

本功能实现了调度请求提交后，接口返回电文数据的处理，包括：
1. **设备移动路径处理** - 根据电文中的 `path` 数据移动指定设备
2. **导航路线显示** - 根据电文中的 `route` 数据显示导航路线

## 电文数据结构

接口返回的电文数据应符合以下结构（参考 `schedule_result.json`）：

```json
{
  "path": [
    {
      "device_id": 0,
      "path": [
        {
          "consume": 0,
          "duration": 1000,
          "position": {
            "x": 1200,
            "y": 0,
            "z": -435
          },
          "power": 100,
          "rotation": {
            "x": 0,
            "y": -90,
            "z": 0
          },
          "shType": "body",
          "componentTurn": []
        }
      ],
      "start_time": 0
    }
  ],
  "route": [
    {
      "device_id": "device_26482",
      "path": [
        {
          "x": 100,
          "z": -200
        },
        {
          "x": 400,
          "z": -200
        }
      ]
    }
  ]
}
```

## 数据字段说明

### path 数据（设备移动）
- `device_id`: 设备ID，对应场景中的车辆模型
- `start_time`: 开始时间（毫秒）
- `path`: 路径点数组
  - `position`: 位置坐标 {x, y, z}
  - `rotation`: 旋转角度 {x, y, z}（度）
  - `duration`: 到达此点的持续时间（毫秒）
  - `power`: 电量
  - `consume`: 消耗
  - `shType`/`meshType`: 网格类型（"body" 或 "component"）
  - `componentTurn`: 组件旋转数组

### route 数据（导航路线）
- `device_id`: 设备ID
- `path`: 路线拐点数组
  - `x`, `z`: 拐点坐标

## 使用方法

### 1. 正常使用流程
1. 在调度请求表单中填写坐标点
2. 点击"提交调度请求"
3. 系统将请求发送到后台API
4. 后台返回电文数据
5. 系统自动处理电文数据：
   - 移动设备到指定路径
   - 显示导航路线

### 2. 测试模式
点击"测试电文处理"按钮可以使用模拟数据测试功能。

### 3. 控制台测试
在浏览器控制台中可以调用：
```javascript
// 测试调度电文处理
window.testScheduleResponse()
```

## 技术实现

### 核心函数

1. **handleScheduleSubmit()** - 处理调度请求提交
2. **processScheduleResponse()** - 处理电文响应数据
3. **processDeviceMovementPaths()** - 处理设备移动路径
4. **processNavigationRoutes()** - 处理导航路线
5. **convertPathDataToSimulatorFormat()** - 转换数据格式

### 对接的现有功能

1. **RouteSimulator.js** - 设备移动功能
   - 使用 `handleVehicleControlMessage()` 方法
   - 支持流畅动画和精确时间控制

2. **RouteRenderer.js** - 导航路线显示功能
   - 使用 `parseAndCreateRoutes()` 方法
   - 支持路线显示/隐藏切换

## 注意事项

1. **设备ID映射**: 确保电文中的 `device_id` 与场景中的车辆模型ID对应
2. **坐标系统**: 电文中的坐标会自动转换为场景坐标系
3. **时间控制**: 设备移动基于 `start_time` 和 `duration` 进行精确控制
4. **路线显示**: 导航路线默认自动显示，可通过右侧面板控制显示/隐藏

## 调试信息

系统会在控制台输出详细的调试信息：
- 📋 调度请求接收
- 🔄 电文数据处理
- 🚛 设备移动路径处理
- 🛣️ 导航路线处理
- ✅ 处理完成状态

## 错误处理

如果处理过程中出现错误，系统会：
1. 在控制台输出详细错误信息
2. 弹出错误提示框
3. 不影响其他功能的正常使用
