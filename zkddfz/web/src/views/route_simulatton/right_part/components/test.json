  [
   {
     timestamp: Date.now(),
     position: { x: 100, y: 0, z: -120 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向北方
     duration: 1000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now(),
     position: { x: 100, y: 0, z: 25 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向北方
     duration: 1000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 1000,
     position: { x: 100, y: 0, z: 68 },
     rotation: { x: 0, y: 90, z: 0 }, // 继续向北
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },


   {
     timestamp: Date.now() + 1000,
     position: { x: 100, y: 0, z: 68 },
     rotation: { x: 0, y: 90, z: 0 }, // 继续向北
     duration: 2000,
     meshName: "履带2" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 1000,
     position: { x: 100, y: 0, z: 68 },
     rotation: { x: 0, y: 90, z: 0 }, // 继续向北
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 1000,
     position: { x: 100, y: 0, z: 68 },
     rotation: { x: 0, y: 90, z: 0 }, // 继续向北
     duration: 2000,
     meshName: "履带3" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 4000,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 90, z: 0 }, // 东北方向
     duration: 2500,
     meshName: "body" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 4000,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 6500,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 65, z: 0 }, // 面向东方
     duration: 3000,
     meshName: "body" // 指定mesh旋转
   },



   {
     timestamp: Date.now() + 4000,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带2" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 4000,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 4000,
     position: { x: 85, y: 0, z: 68 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带3" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 9500,
     position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
     rotation: { x: 0, y: 65, z: 0 }, // 东南方向
     duration: 2000,
     meshName: "body" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 9500,
     position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
     rotation: { x: 0, y: 90, z: 0 }, // 东南方向
     duration: 2000,
     meshName: "履带2" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 9500,
     position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
     rotation: { x: 0, y: 90, z: 0 }, // 东南方向
     duration: 2000,
     meshName: "履带3" // 指定mesh旋转
   },


   {
     timestamp: Date.now() + 11500,
     position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
     rotation: { x: 0, y: 55, z: 0 }, // 面向南方
     duration: 4000,
     meshName: "body" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 11500,
     position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向南方
     duration: 2000,
     meshName: "履带1" // 指定mesh旋转
   },


   {
     timestamp: Date.now() + 15500,
     position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
     rotation: { x: 0, y: 55, z: 0 }, // 西南方向
     duration: 3500,
     meshName: "body" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 15500,
     position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
     rotation: { x: 0, y: 0, z: 0 }, // 西南方向
     duration: 2000,
     meshName: "履带1" // 指定mesh旋转
   },


   {
     timestamp: Date.now() + 19000,
     position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
     rotation: { x: 0, y: 45, z: 0 }, // 面向西方
     duration: 4000,
     meshName: "body" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 19000,
     position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向西方
     duration: 2000,
     meshName: "履带2" // 指定mesh旋转
   },
   {
     timestamp: Date.now() + 19000,
     position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向西方
     duration: 2000,
     meshName: "履带3" // 指定mesh旋转
   },

   {
     timestamp: Date.now() + 23000,
     position: { x: 84.2139333204883, y: 0, z: 77.8321035155745 },
     rotation: { x: 0, y: 45, z: 0 }, // 西北方向
     duration: 3000,
     meshName: "body" // 指定mesh旋转
   },


   {
     timestamp: Date.now() + 26000,
     position: { x: 89.5172341793874, y: 0, z: 83.1354043744736 },
     rotation: { x: 0, y: 45, z: 0 }, // 面向北方
     duration: 3500,
     meshName: "body" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 26000,
     position: { x: 89.5172341793874, y: 0, z: 83.1354043744736 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向北方
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 26000,
     position: { x: 89.5172341793874, y: 0, z: 83.1354043744736 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向北方
     duration: 2000,
     meshName: "履带2" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 26000,
     position: { x: 89.5172341793874, y: 0, z: 83.1354043744736 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向北方
     duration: 2000,
     meshName: "履带3" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 29500,
     position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
     rotation: { x: 0, y: 45, z: 0 }, // 东北方向
     duration: 2500,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 29500,
     position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2500,
     meshName: "履带1" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 32000,
     position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
     rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 29500,
     position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
     rotation: { x: 0, y: 90, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 32000,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 29500,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 0, z: 0 }, // 东北方向
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },


   {
     timestamp: Date.now() + 32000,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向东方停车
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向东方停车
     duration: 2000,
     meshName: "履带2" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向东方停车
     duration: 2000,
     meshName: "履带3" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 32000,
     position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
     rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },


   {
     timestamp: Date.now() + 32000,
     position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
     rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
     rotation: { x: 0, y: 90, z: 0 }, // 面向东方停车
     duration: 2000,
     meshName: "履带1" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
     rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "履带1" // 整体模型旋转
   },

   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "履带2" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "履带3" // 整体模型旋转
   },


   {
     timestamp: Date.now() + 32000,
     position: { x: 75.927308061452, y: 0, z: 115.321997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
   {
     timestamp: Date.now() + 32000,
     position: { x: 118.427308061452, y: 0, z: 115.321997906308 },
     rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
     duration: 3000,
     meshName: "body" // 整体模型旋转
   },
 ]