<script setup>
import { ref, onMounted, onBeforeUnmount, watch, toRaw } from 'vue';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { EasingFunctions, createAnimation, calculateDistance } from '../utils/animationUtils.js';

const props = defineProps({
  // 模型配置
  modelConfig: {
    type: Object,
    required: true,
  },
  // 场景实例（由父组件提供）
  scene: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['loaded', 'error']);

// 模型状态
const isLoaded = ref(false);
const loadingProgress = ref(0);
const modelObject = ref(null);
const mixer = ref(null);
const animations = ref([]);
const currentAnimation = ref(null);

// 运动动画相关状态
const isMoving = ref(false);
const movementAnimation = ref(null);
const currentPosition = ref(new THREE.Vector3());
const targetPosition = ref(new THREE.Vector3());
const movementSpeed = ref(2.0); // 默认移动速度 (单位/秒)
const boundingBox = ref(null);
const groundOffset = ref(0); // 模型底部到地面的偏移量

// 加载模型
const loadModel = () => {
  const loader = new GLTFLoader();

  try {
    loader.load(
      props.modelConfig.path,
      gltf => {
        try {
          // 保存模型引用
          modelObject.value = gltf.scene;

          // 计算模型边界框和地面偏移
          calculateBoundingBox();

          // 设置模型位置、旋转和缩放
          updateModelTransform();

          // 处理动画
          if (gltf.animations && gltf.animations.length) {
            animations.value = gltf.animations;
            mixer.value = new THREE.AnimationMixer(gltf.scene);

            // 如果配置中指定了默认动画，则仿真
            if (props.modelConfig.defaultAnimation !== undefined) {
              playAnimation(props.modelConfig.defaultAnimation);
            }
          }

          // 增强模型材质和阴影
          gltf.scene.traverse(node => {
            if (node.isMesh) {
              // 启用阴影
              node.castShadow = true;
              node.receiveShadow = true;

              // 确保使用PBR材质
              if (!node.material.isMeshStandardMaterial && !node.material.isMeshPhysicalMaterial) {
                // 转换为标准PBR材质
                const oldMaterial = node.material;
                node.material = new THREE.MeshStandardMaterial({
                  color: oldMaterial.color || 0xffffff,
                  map: oldMaterial.map,
                  normalMap: oldMaterial.normalMap,
                  roughnessMap: oldMaterial.roughnessMap,
                  metalnessMap: oldMaterial.metalnessMap,
                  aoMap: oldMaterial.aoMap,
                  emissiveMap: oldMaterial.emissiveMap,
                  transparent: oldMaterial.transparent,
                  opacity: oldMaterial.opacity,
                  side: oldMaterial.side,
                });
              }

              // 设置高质量PBR属性
              node.material.metalness = node.material.metalness || 0.1;
              node.material.roughness = node.material.roughness || 0.8;
              node.material.envMapIntensity = 1.0;

              // 如果配置了材质覆盖
              if (props.modelConfig.material) {
                if (props.modelConfig.material.color) {
                  node.material.color.set(props.modelConfig.material.color);
                }
                if (props.modelConfig.material.metalness !== undefined) {
                  node.material.metalness = props.modelConfig.material.metalness;
                }
                if (props.modelConfig.material.roughness !== undefined) {
                  node.material.roughness = props.modelConfig.material.roughness;
                }
                if (props.modelConfig.material.envMapIntensity !== undefined) {
                  node.material.envMapIntensity = props.modelConfig.material.envMapIntensity;
                }
              }

              // 标记材质需要更新
              node.material.needsUpdate = true;
            }
          });

          // 确保模型对象不被Vue响应式系统代理
          // 这是关键步骤，防止Three.js内部访问代理属性时出错
          const rawScene = toRaw(gltf.scene);

          // 递归确保所有子对象也不被代理
          rawScene.traverse(child => {
            // 强制转换为原始对象，防止Vue代理
            Object.setPrototypeOf(child, Object.getPrototypeOf(toRaw(child)));
          });

          // 添加到场景 - 使用处理过的原始对象
          toRaw(props.scene).add(rawScene);

          // 保存模型引用 - 使用原始对象
          modelObject.value = rawScene;

          // 更新状态
          isLoaded.value = true;
          emit('loaded', {
            id: props.modelConfig.id,
            model: rawScene,
          });
        } catch (error) {
          console.error('处理模型时出错:', error);
          emit('error', {
            id: props.modelConfig.id,
            error: error.message,
          });
        }
      },
      xhr => {
        loadingProgress.value = Math.floor((xhr.loaded / xhr.total) * 100);
      },
      error => {
        console.error('加载模型失败:', error);
        emit('error', {
          id: props.modelConfig.id,
          error: error.message,
        });
      },
    );
  } catch (error) {
    console.error('初始化加载器失败:', error);
    emit('error', {
      id: props.modelConfig.id,
      error: error.message,
    });
  }
};

// 计算模型边界框和地面偏移
const calculateBoundingBox = () => {
  if (!modelObject.value) return;

  const box = new THREE.Box3().setFromObject(modelObject.value);
  boundingBox.value = box;

  // 计算模型底部到原点的距离，用于地面定位
  groundOffset.value = -box.min.y;

  console.log(`模型 ${props.modelConfig.id} 边界框:`, box);
  console.log(`地面偏移量: ${groundOffset.value}`);
};

// 更新模型变换（位置、旋转、缩放）
const updateModelTransform = () => {
  if (!modelObject.value) return;

  const model = modelObject.value;
  const config = props.modelConfig;

  // 更新位置 - 优先使用currentPosition（实时位置），如果没有则使用initialPosition，最后使用position（向后兼容）
  const positionConfig = config.currentPosition || config.initialPosition || config.position;
  if (positionConfig) {
    let yPosition = positionConfig.y !== undefined ? positionConfig.y : 0;

    // 如果启用了地面定位且y位置为0，则自动调整到地面
    if (config.autoGroundPosition !== false && yPosition === 0 && groundOffset.value > 0) {
      yPosition = groundOffset.value;
    }

    const newPosition = new THREE.Vector3(
      positionConfig.x !== undefined ? positionConfig.x : 0,
      yPosition,
      positionConfig.z !== undefined ? positionConfig.z : 0,
    );

    model.position.copy(newPosition);
    currentPosition.value.copy(newPosition);

    // 只有在使用initialPosition时才更新currentPosition，避免循环更新
    if (config.currentPosition && positionConfig === config.initialPosition) {
      config.currentPosition.x = positionConfig.x;
      config.currentPosition.y = positionConfig.y;
      config.currentPosition.z = positionConfig.z;
    }
  }

  // 更新旋转 - 优先使用currentRotation（实时角度），如果没有则使用initialRotation，最后使用rotation（向后兼容）
  const rotationConfig = config.currentRotation || config.initialRotation || config.rotation;
  if (rotationConfig) {
    const newRotation = new THREE.Euler(
      THREE.MathUtils.degToRad(rotationConfig.x || 0),
      THREE.MathUtils.degToRad(rotationConfig.y || 0),
      THREE.MathUtils.degToRad(rotationConfig.z || 0),
    );
    model.rotation.copy(newRotation);

    // 只有在使用initialRotation时才更新currentRotation，避免循环更新
    if (config.currentRotation && rotationConfig === config.initialRotation) {
      config.currentRotation.x = rotationConfig.x;
      config.currentRotation.y = rotationConfig.y;
      config.currentRotation.z = rotationConfig.z;
    }
  }

  // 更新缩放
  if (config.scale) {
    const scaleValue = typeof config.scale === 'number' ? config.scale : 1;

    const newScale = new THREE.Vector3(
      config.scale.x !== undefined ? config.scale.x : scaleValue,
      config.scale.y !== undefined ? config.scale.y : scaleValue,
      config.scale.z !== undefined ? config.scale.z : scaleValue,
    );
    model.scale.copy(newScale);
  }

  // 更新移动速度
  if (config.movementSpeed !== undefined) {
    movementSpeed.value = config.movementSpeed;
  }
};

// 仿真动画
const playAnimation = index => {
  if (!mixer.value || !animations.value.length) return;

  try {
    // 停止当前动画
    if (currentAnimation.value) {
      currentAnimation.value.stop();
    }

    // 仿真新动画
    if (animations.value[index]) {
      currentAnimation.value = mixer.value.clipAction(animations.value[index]);
      currentAnimation.value.play();
    }
  } catch (error) {
    console.error('仿真动画失败:', error);
  }
};

// 转换地面坐标系到世界坐标系
const convertGroundToWorldPosition = groundPos => {
  // 地面坐标系：Y=0 表示地面，以地面为基准
  // 世界坐标系：需要加上地面偏移量
  return {
    x: groundPos.x !== undefined ? groundPos.x : 0,
    y: groundPos.y !== undefined ? groundPos.y + (groundOffset.value || 0) : groundOffset.value || 0,
    z: groundPos.z !== undefined ? groundPos.z : 0,
  };
};

// 转换世界坐标系到地面坐标系
const convertWorldToGroundPosition = worldPos => {
  // 世界坐标系转换为地面坐标系：减去地面偏移量
  return {
    x: worldPos.x,
    y: worldPos.y - (groundOffset.value || 0),
    z: worldPos.z,
  };
};

// 立即设置位置（用于重置等操作）
const setPositionImmediate = targetPos => {
  if (!modelObject.value) return;

  // 停止当前移动动画
  if (movementAnimation.value) {
    movementAnimation.value.stop();
    movementAnimation.value = null;
  }

  // 转换地面坐标系到世界坐标系
  const worldPos = convertGroundToWorldPosition(targetPos);

  const newPosition = new THREE.Vector3(worldPos.x, worldPos.y, worldPos.z);

  modelObject.value.position.copy(newPosition);
  currentPosition.value.copy(newPosition);
  isMoving.value = false;

  console.log(`模型 ${props.modelConfig.id} 立即设置到位置:`, targetPos, '(地面坐标系)', '-> 世界坐标:', worldPos);
};

// 直接设置位置（用于WebSocket实时数据，不进行坐标转换）
const setPositionImmediateRaw = targetPos => {
  if (!modelObject.value) return;

  // 停止当前移动动画
  if (movementAnimation.value) {
    movementAnimation.value.stop();
    movementAnimation.value = null;
  }

  // 直接使用原始坐标，不进行任何转换
  const newPosition = new THREE.Vector3(
    targetPos.x !== undefined ? targetPos.x : 0,
    targetPos.y !== undefined ? targetPos.y : 0, // 直接使用原始Y坐标
    targetPos.z !== undefined ? targetPos.z : 0
  );

  modelObject.value.position.copy(newPosition);
  currentPosition.value.copy(newPosition);
  isMoving.value = false;

  console.log(`模型 ${props.modelConfig.id} 直接设置到位置:`, targetPos, '-> 世界坐标:', newPosition);
};

// 平滑移动到目标位置（默认使用匀速线性动画）
const moveToPosition = (targetPos, speed = null, easingType = 'linear', immediate = false) => {
  if (!modelObject.value) return;

  // 如果是立即移动（如重置操作），直接设置位置
  if (immediate) {
    setPositionImmediate(targetPos);
    return Promise.resolve();
  }

  const actualSpeed = speed !== null ? speed : movementSpeed.value;

  // 转换地面坐标系到世界坐标系
  const worldTargetPos = convertGroundToWorldPosition(targetPos);

  targetPosition.value.set(worldTargetPos.x, worldTargetPos.y, worldTargetPos.z);

  // 如果已经在目标位置，直接返回
  const distance = calculateDistance(currentPosition.value, targetPosition.value);
  if (distance < 0.01) {
    isMoving.value = false;
    return Promise.resolve();
  }

  // 停止当前移动动画
  if (movementAnimation.value) {
    movementAnimation.value.stop();
  }

  isMoving.value = true;
  const startPosition = currentPosition.value.clone();
  const duration = (distance / actualSpeed) * 1000; // 转换为毫秒

  console.log(
    `开始移动模型 ${props.modelConfig.id} 到地面坐标:`,
    targetPos,
    '-> 世界坐标:',
    worldTargetPos,
    `距离: ${distance.toFixed(2)}, 持续时间: ${duration.toFixed(0)}ms`,
  );

  return new Promise(resolve => {
    movementAnimation.value = createAnimation({
      duration: duration,
      easing: EasingFunctions[easingType] || EasingFunctions.linear, // 默认使用匀速线性动画
      onUpdate: easedProgress => {
        if (!modelObject.value) return;

        // 使用Three.js的Vector3.lerpVectors进行插值
        const newPosition = new THREE.Vector3().lerpVectors(startPosition, targetPosition.value, easedProgress);

        modelObject.value.position.copy(newPosition);
        currentPosition.value.copy(newPosition);
      },
      onComplete: () => {
        isMoving.value = false;
        movementAnimation.value = null;
        console.log(`模型 ${props.modelConfig.id} 移动完成`);
        resolve();
      },
    });

    movementAnimation.value.start();
  });
};

// 停止移动
const stopMovement = () => {
  if (movementAnimation.value) {
    movementAnimation.value.stop();
    movementAnimation.value = null;
  }
  isMoving.value = false;
  console.log(`模型 ${props.modelConfig.id} 移动已停止`);
};

// 更新动画
const updateAnimation = delta => {
  // 更新模型自身的动画
  if (mixer.value) {
    try {
      mixer.value.update(delta);
    } catch (error) {
      console.error('更新动画失败:', error);
    }
  }

  // 注意：移动动画现在使用requestAnimationFrame，不需要在这里更新
};

// 获取当前位置（地面坐标系）
const getCurrentPosition = () => {
  return convertWorldToGroundPosition(currentPosition.value);
};

// 获取当前世界坐标位置
const getCurrentWorldPosition = () => {
  return currentPosition.value.clone();
};

// 设置移动速度
const setMovementSpeed = speed => {
  movementSpeed.value = speed;
};

// 获取模型边界框信息
const getBoundingBox = () => {
  return boundingBox.value;
};

// 获取地面偏移量
const getGroundOffset = () => {
  return groundOffset.value;
};

// 更新模型的当前坐标（用于JSON数据更新）
const updateCurrentPosition = newPosition => {
  if (!modelObject.value) return;

  // 更新配置中的currentPosition
  if (props.modelConfig.currentPosition) {
    props.modelConfig.currentPosition.x = newPosition.x;
    props.modelConfig.currentPosition.y = newPosition.y;
    props.modelConfig.currentPosition.z = newPosition.z;
  }

  // 对于WebSocket实时数据，直接设置位置，不进行坐标转换
  setPositionImmediateRaw(newPosition);
};

// 更新模型的当前角度（用于JSON数据更新）
const updateCurrentRotation = newRotation => {
  if (!modelObject.value) return;

  // 更新配置中的currentRotation
  if (props.modelConfig.currentRotation) {
    props.modelConfig.currentRotation.x = newRotation.x;
    props.modelConfig.currentRotation.y = newRotation.y;
    props.modelConfig.currentRotation.z = newRotation.z;
  }

  // 立即设置模型旋转
  const euler = new THREE.Euler(
    THREE.MathUtils.degToRad(newRotation.x || 0),
    THREE.MathUtils.degToRad(newRotation.y || 0),
    THREE.MathUtils.degToRad(newRotation.z || 0),
  );
  modelObject.value.rotation.copy(euler);
};

// 重置到初始位置和角度
const resetToInitialTransform = () => {
  if (!modelObject.value || !props.modelConfig) return;

  // 重置位置
  if (props.modelConfig.initialPosition) {
    updateCurrentPosition(props.modelConfig.initialPosition);
  }

  // 重置角度
  if (props.modelConfig.initialRotation) {
    updateCurrentRotation(props.modelConfig.initialRotation);
  }

  console.log(`模型 ${props.modelConfig.id} 已重置到初始位置和角度`);
};

// 获取当前角度（度数）
const getCurrentRotation = () => {
  if (!modelObject.value) return { x: 0, y: 0, z: 0 };

  return {
    x: THREE.MathUtils.radToDeg(modelObject.value.rotation.x),
    y: THREE.MathUtils.radToDeg(modelObject.value.rotation.y),
    z: THREE.MathUtils.radToDeg(modelObject.value.rotation.z),
  };
};

// 监听配置变化，更新模型
watch(
  () => props.modelConfig,
  newConfig => {
    try {
      updateModelTransform();

      // 如果配置了新的默认动画
      if (newConfig.defaultAnimation !== undefined && mixer.value) {
        playAnimation(newConfig.defaultAnimation);
      }
    } catch (error) {
      console.error('更新模型配置失败:', error);
    }
  },
  { deep: true },
);

// 在组件挂载后加载模型
onMounted(() => {
  loadModel();
});

// 在组件卸载前清理资源
onBeforeUnmount(() => {
  try {
    // 停止移动动画
    if (movementAnimation.value) {
      movementAnimation.value.stop();
    }

    if (modelObject.value && props.scene) {
      toRaw(props.scene).remove(modelObject.value);
    }

    if (mixer.value) {
      mixer.value.stopAllAction();
    }
  } catch (error) {
    console.error('清理资源失败:', error);
  }
});

// 暴露方法给父组件
defineExpose({
  updateAnimation,
  playAnimation,
  moveToPosition,
  setPositionImmediate,
  setPositionImmediateRaw,
  getCurrentPosition,
  getCurrentWorldPosition,
  getCurrentRotation,
  updateCurrentPosition,
  updateCurrentRotation,
  resetToInitialTransform,
  setMovementSpeed,
  stopMovement,
  getBoundingBox,
  getGroundOffset,
  convertGroundToWorldPosition,
  convertWorldToGroundPosition,
  getModel: () => modelObject.value,
  isLoaded,
  loadingProgress,
  isMoving,
});
</script>

<template>
  <!-- 这个组件不渲染任何DOM元素，只负责加载和管理3D模型 -->
</template>
