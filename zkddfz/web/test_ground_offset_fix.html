<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地面偏移量修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-case {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .expected-result {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>地面偏移量修复测试</h1>
        <p>本测试验证模型底部贴紧地面的功能是否正确实现。</p>

        <div class="test-section">
            <div class="test-title">修复内容说明</div>
            <div class="test-description">
                <p><strong>问题：</strong>之前的实现只有在Y坐标为0时才会应用地面偏移，导致模型可能悬浮在空中。</p>
                <p><strong>解决方案：</strong>修改了 <code>updateModelTransform</code> 和 <code>setPositionImmediateRaw</code> 函数，
                现在无论Y坐标是什么值，都会自动应用地面偏移量，确保模型底部贴紧地面。</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试用例 1: Y=0 的模型（向后兼容性测试）</div>
            <div class="test-case">
                <strong>配置：</strong>
                <div class="code-block">
{
  id: 'test1',
  initialPosition: { x: 0, y: 0, z: 0 },
  autoGroundPosition: true
}
                </div>
                <div class="expected-result">
                    <strong>预期结果：</strong>模型底部应该贴紧地面（Y坐标 = groundOffset）
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试用例 2: Y>0 的模型（新功能测试）</div>
            <div class="test-case">
                <strong>配置：</strong>
                <div class="code-block">
{
  id: 'test2',
  initialPosition: { x: 0, y: 5, z: 0 },
  autoGroundPosition: true
}
                </div>
                <div class="expected-result">
                    <strong>预期结果：</strong>模型底部应该距离地面5个单位（Y坐标 = 5 + groundOffset）
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试用例 3: 禁用自动地面定位</div>
            <div class="test-case">
                <strong>配置：</strong>
                <div class="code-block">
{
  id: 'test3',
  initialPosition: { x: 0, y: 10, z: 0 },
  autoGroundPosition: false
}
                </div>
                <div class="expected-result">
                    <strong>预期结果：</strong>模型应该直接放在Y=10的位置，不应用地面偏移
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试用例 4: WebSocket实时数据更新</div>
            <div class="test-case">
                <strong>操作：</strong>
                <div class="code-block">
// 调用 updateCurrentPosition 或 setPositionImmediateRaw
updateCurrentPosition({ x: 10, y: 3, z: 20 })
                </div>
                <div class="expected-result">
                    <strong>预期结果：</strong>模型底部应该距离地面3个单位（Y坐标 = 3 + groundOffset）
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">关键修改点</div>
            <div class="test-description">
                <h4>1. updateModelTransform 函数修改：</h4>
                <div class="code-block">
// 修改前：
if (config.autoGroundPosition !== false && yPosition === 0 && groundOffset.value > 0) {
  yPosition = groundOffset.value;
}

// 修改后：
if (config.autoGroundPosition !== false && groundOffset.value > 0) {
  yPosition = yPosition + groundOffset.value;
}
                </div>

                <h4>2. setPositionImmediateRaw 函数修改：</h4>
                <div class="code-block">
// 新增地面偏移逻辑：
let yPosition = targetPos.y !== undefined ? targetPos.y : 0;

if (props.modelConfig.autoGroundPosition !== false && groundOffset.value > 0) {
  yPosition = yPosition + groundOffset.value;
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">验证步骤</div>
            <div class="test-description">
                <ol>
                    <li>在浏览器控制台中查看模型加载时的日志信息</li>
                    <li>确认每个模型都输出了正确的边界框和地面偏移量</li>
                    <li>验证模型的实际Y坐标是否等于 <span class="highlight">配置Y坐标 + groundOffset</span></li>
                    <li>测试移动模型到不同Y坐标，确认底部始终贴紧相应高度的地面</li>
                    <li>测试禁用 autoGroundPosition 的模型是否不应用偏移</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">预期的控制台输出示例</div>
            <div class="code-block">
模型 test1 边界框: Box3 { min: Vector3 { x: -1, y: -2, z: -1 }, max: Vector3 { x: 1, y: 3, z: 1 } }
地面偏移量: 2 (用于让模型底部贴紧地面)
模型 test1 直接设置到位置: {x: 0, y: 0, z: 0} -> 世界坐标: Vector3 { x: 0, y: 2, z: 0 } (已应用地面偏移: 2)
            </div>
        </div>
    </div>
</body>
</html>
