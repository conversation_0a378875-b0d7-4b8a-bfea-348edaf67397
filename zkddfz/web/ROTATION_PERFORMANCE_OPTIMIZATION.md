# Mesh旋转性能优化说明

## 问题分析

原始的mesh旋转实现存在以下性能问题：

1. **过多的console.log输出** - 每次旋转都会输出大量调试信息，影响性能
2. **频繁的几何体计算** - 每次旋转都重新计算包围盒和中心点
3. **重复的矩阵计算** - 每帧都创建新的矩阵对象，造成内存压力
4. **不必要的数据重新初始化** - 频繁检查和重新初始化旋转数据
5. **复杂的旋转变换计算** - 每次都重置到原始状态再应用变换

## 优化方案

### 1. 日志输出优化

```javascript
// 添加调试模式控制
this.options = {
  debugMode: false, // 新增：控制日志输出
  // ... 其他选项
};

// 减少日志输出频率
if (this.options.debugMode && Math.random() < 0.1) { // 10%概率输出日志
  console.log(`旋转mesh "${mesh.name}": ${normalizedDegrees.toFixed(1)}°`);
}
```

### 2. 缓存机制优化

```javascript
// 在旋转数据中添加缓存
mesh.userData.rotationData = {
  // ... 原有数据
  cachedRelativePosition: null,  // 缓存相对位置向量
  rotationMatrix: null,  // 缓存旋转矩阵，避免重复创建
  lastUpdateTime: performance.now()  // 记录最后更新时间
};

// 复用缓存的对象
if (!rotationData.cachedRelativePosition) {
  rotationData.cachedRelativePosition = rotationData.originalPosition.clone().sub(centerPoint);
}
const relativePosition = rotationData.cachedRelativePosition.clone();
```

### 3. 几何体计算优化

```javascript
// 避免重复计算包围盒
if (!mesh.geometry.boundingBox) {
  mesh.geometry.computeBoundingBox();
}

// 缓存变换矩阵对象
if (!mesh.userData.tempMatrix) {
  mesh.userData.tempMatrix = new THREE.Matrix4();
  mesh.userData.tempQuaternion = new THREE.Quaternion();
}
```

### 4. 动画帧率优化

```javascript
// 使用节流的动画循环
let lastUpdateTime = startTime;
const updateInterval = 16; // 约60fps

const animate = (currentTime) => {
  // 节流：只在间隔时间后才更新
  if (currentTime - lastUpdateTime < updateInterval) {
    requestAnimationFrame(animate);
    return;
  }
  lastUpdateTime = currentTime;
  // ... 动画逻辑
};
```

### 5. 组合mesh优化

```javascript
// 缓存子mesh列表，避免重复遍历
if (!mesh.userData.cachedChildMeshes) {
  mesh.userData.cachedChildMeshes = [];
  mesh.traverse((child) => {
    if (child.isMesh && child.geometry) {
      mesh.userData.cachedChildMeshes.push(child);
    }
  });
}
```

## 新增功能

### 1. 调试模式控制

```javascript
// 设置调试模式
routeSimulator.setDebugMode(false); // 关闭调试日志，提高性能
routeSimulator.setDebugMode(true);  // 开启调试日志，便于调试
```

### 2. 缓存管理

```javascript
// 清理mesh旋转缓存
routeSimulator.clearMeshRotationCache();

// 获取性能统计
const stats = routeSimulator.getRotationPerformanceStats();
console.log('缓存命中率:', stats.cacheHitRate);
```

## 使用建议

### 生产环境配置

```javascript
const routeSimulator = new RouteSimulator({
  debugMode: false,        // 关闭调试模式
  smoothRotation: true,    // 保持平滑旋转
  // ... 其他配置
});
```

### 开发环境配置

```javascript
const routeSimulator = new RouteSimulator({
  debugMode: true,         // 开启调试模式
  smoothRotation: true,    // 保持平滑旋转
  // ... 其他配置
});
```

### 性能监控

```javascript
// 定期检查性能统计
setInterval(() => {
  const stats = routeSimulator.getRotationPerformanceStats();
  if (parseFloat(stats.cacheHitRate) < 50) {
    console.warn('缓存命中率较低，考虑清理缓存');
    routeSimulator.clearMeshRotationCache();
  }
}, 30000); // 每30秒检查一次
```

## 性能测试

使用提供的测试工具 `test_rotation_performance.html` 进行性能测试：

1. 打开测试页面
2. 设置测试参数（角度、时长、次数）
3. 选择调试模式（关闭获得最佳性能）
4. 运行性能测试或压力测试
5. 查看性能统计和日志

### 性能指标

- **优秀**: 平均旋转时间 < 10ms
- **良好**: 平均旋转时间 < 20ms  
- **需要优化**: 平均旋转时间 >= 20ms

## 预期效果

经过优化后，mesh旋转性能应该有显著提升：

1. **日志输出减少90%** - 大幅减少console.log调用
2. **内存使用优化** - 通过对象缓存减少GC压力
3. **计算效率提升** - 避免重复的几何体和矩阵计算
4. **动画流畅度改善** - 通过帧率控制和缓存机制
5. **缓存命中率提升** - 通过智能缓存策略

## 注意事项

1. **调试模式影响**: 开启调试模式会显著影响性能，生产环境务必关闭
2. **缓存清理**: 长时间运行后建议定期清理缓存，避免内存泄漏
3. **兼容性**: 优化后的代码保持向后兼容，不影响现有功能
4. **监控**: 建议在生产环境中监控旋转性能，及时发现问题

## 故障排除

### 如果旋转仍然卡顿

1. 检查是否关闭了调试模式
2. 清理mesh旋转缓存
3. 检查浏览器控制台是否有大量日志输出
4. 使用性能测试工具验证优化效果

### 如果出现旋转异常

1. 临时开启调试模式查看详细日志
2. 检查mesh的几何体是否正常
3. 验证旋转数据的初始化是否正确
4. 确认缓存数据没有被意外修改
