<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试旋转中心点修复</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-line;
        }
        
        #canvas-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试旋转中心点修复</h1>
        
        <div class="controls">
            <h3>旋转控制</h3>
            
            <div class="control-group">
                <label>旋转角度:</label>
                <input type="range" id="rotationSlider" min="0" max="360" value="0" step="1">
                <span id="rotationValue">0°</span>
            </div>
            
            <div class="control-group">
                <button onclick="resetRotation()">重置旋转</button>
                <button onclick="testMultipleRotations()">测试多次旋转</button>
                <button onclick="testContinuousRotation()">连续旋转测试</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="info" id="logInfo">
等待测试开始...
            </div>
        </div>
        
        <div id="canvas-container">
            <canvas id="testCanvas" width="800" height="600"></canvas>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.155.0/build/three.module.js';
        
        // 全局变量
        let scene, camera, renderer, testMesh, routeSimulator;
        let rotationSlider, rotationValue, logInfo;
        
        // 模拟 RouteSimulator 的旋转方法
        class TestRouteSimulator {
            constructor() {
                this.model = null;
            }
            
            setModel(model) {
                this.model = model;
            }
            
            // 复制修复后的方法
            rotateMeshByName(meshName, yRotationDegrees) {
                if (!this.model) {
                    console.warn('Model not available for mesh rotation');
                    return;
                }

                console.log(`旋转mesh: ${meshName}, Y轴角度: ${yRotationDegrees}度`);

                // 递归查找指定名称的mesh
                const targetMesh = this.findMeshByName(this.model, meshName);

                if (targetMesh) {
                    // 计算mesh的中心点并以中心点为轴旋转
                    this.rotateMeshAroundCenter(targetMesh, yRotationDegrees);
                    console.log(`成功旋转mesh: ${meshName}`);
                } else {
                    console.warn(`未找到名称为 "${meshName}" 的mesh`);
                }
            }
            
            rotateMeshAroundCenter(mesh, yRotationDegrees) {
                if (!mesh) return;

                // 检查是否需要重新初始化旋转数据
                if (!mesh.userData.rotationData || this.shouldReinitializeRotationData(mesh)) {
                    console.log(`重新初始化mesh "${mesh.name}" 的旋转数据`);
                    this.initializeMeshRotationData(mesh);
                }

                const rotationData = mesh.userData.rotationData;
                const targetRotationRad = THREE.MathUtils.degToRad(yRotationDegrees);

                // 计算旋转变换
                this.applyRotationAroundPoint(mesh, rotationData.center, targetRotationRad);

                // 存储当前旋转角度
                mesh.userData.currentRotationY = yRotationDegrees;

                console.log(`mesh "${mesh.name}" 中心点旋转: ${yRotationDegrees}°`);
                console.log(`中心点: (${rotationData.center.x.toFixed(2)}, ${rotationData.center.y.toFixed(2)}, ${rotationData.center.z.toFixed(2)})`);
            }
            
            shouldReinitializeRotationData(mesh) {
                if (!mesh.userData.rotationData) return true;

                const rotationData = mesh.userData.rotationData;
                
                // 检查原始位置是否发生变化
                const positionChanged = !mesh.position.equals(rotationData.originalPosition);
                
                if (positionChanged) {
                    console.log(`检测到mesh "${mesh.name}" 位置发生变化，需要重新初始化旋转数据`);
                    console.log(`- 当前位置: (${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)})`);
                    console.log(`- 原始位置: (${rotationData.originalPosition.x.toFixed(2)}, ${rotationData.originalPosition.y.toFixed(2)}, ${rotationData.originalPosition.z.toFixed(2)})`);
                    return true;
                }

                return false;
            }
            
            applyRotationAroundPoint(mesh, centerPoint, rotationRad) {
                if (!mesh || !centerPoint) return;

                const rotationData = mesh.userData.rotationData;
                if (!rotationData) return;

                // 重置到原始状态
                mesh.position.copy(rotationData.originalPosition);
                mesh.rotation.copy(rotationData.originalRotation);
                mesh.scale.copy(rotationData.originalScale);

                // 使用存储的原始中心点
                const rotationCenter = rotationData.center.clone();

                // 计算mesh原始位置相对于旋转中心的位置
                const relativePosition = rotationData.originalPosition.clone().sub(rotationCenter);

                // 创建Y轴旋转矩阵
                const rotationMatrix = new THREE.Matrix4().makeRotationY(rotationRad);

                // 应用旋转到相对位置
                relativePosition.applyMatrix4(rotationMatrix);

                // 设置新的位置
                mesh.position.copy(rotationCenter).add(relativePosition);

                // 应用旋转到mesh本身
                mesh.rotation.y = rotationData.originalRotation.y + rotationRad;

                console.log(`应用旋转变换:`);
                console.log(`- 旋转中心: (${rotationCenter.x.toFixed(2)}, ${rotationCenter.y.toFixed(2)}, ${rotationCenter.z.toFixed(2)})`);
                console.log(`- 原始位置: (${rotationData.originalPosition.x.toFixed(2)}, ${rotationData.originalPosition.y.toFixed(2)}, ${rotationData.originalPosition.z.toFixed(2)})`);
                console.log(`- 新位置: (${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)})`);
                console.log(`- 新旋转: ${THREE.MathUtils.radToDeg(mesh.rotation.y).toFixed(2)}°`);
            }
            
            initializeMeshRotationData(mesh) {
                if (!mesh) return;

                // 确保mesh的矩阵是最新的
                mesh.updateMatrixWorld(true);

                // 计算mesh的几何中心点
                const center = this.calculateMeshGeometryCenter(mesh);

                // 存储原始变换矩阵和中心点
                mesh.userData.rotationData = {
                    originalMatrix: mesh.matrix.clone(),
                    center: center.clone(),
                    originalPosition: mesh.position.clone(),
                    originalRotation: mesh.rotation.clone(),
                    originalScale: mesh.scale.clone(),
                    originalWorldMatrix: mesh.matrixWorld.clone()
                };

                // 初始化当前旋转角度
                mesh.userData.currentRotationY = THREE.MathUtils.radToDeg(mesh.rotation.y);

                console.log(`初始化mesh "${mesh.name}" 旋转数据:`);
                console.log(`- 几何中心点: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);
                console.log(`- 原始位置: (${mesh.position.x.toFixed(2)}, ${mesh.position.y.toFixed(2)}, ${mesh.position.z.toFixed(2)})`);
                console.log(`- 初始旋转: ${mesh.userData.currentRotationY.toFixed(2)}°`);
            }
            
            calculateMeshGeometryCenter(mesh) {
                if (!mesh) return new THREE.Vector3();

                let center = new THREE.Vector3();

                // 如果是Mesh对象且有geometry
                if (mesh.isMesh && mesh.geometry) {
                    // 计算几何体的包围盒
                    mesh.geometry.computeBoundingBox();
                    if (mesh.geometry.boundingBox) {
                        // 获取几何体的本地中心点
                        mesh.geometry.boundingBox.getCenter(center);

                        // 创建变换矩阵
                        const tempMatrix = new THREE.Matrix4();
                        tempMatrix.compose(
                            mesh.position.clone(),
                            new THREE.Quaternion().setFromEuler(mesh.rotation.clone()),
                            mesh.scale.clone()
                        );

                        // 将几何中心转换到世界坐标系
                        center.applyMatrix4(tempMatrix);

                        console.log(`mesh "${mesh.name}" 几何包围盒中心: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);
                        return center;
                    }
                }

                // 备用方案：使用对象的位置作为中心点
                center.copy(mesh.position);
                console.log(`mesh "${mesh.name}" 使用位置作为中心点: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);

                return center;
            }
            
            findMeshByName(object, name) {
                if (object.name === name) {
                    return object;
                }

                if (object.children) {
                    for (let child of object.children) {
                        const found = this.findMeshByName(child, name);
                        if (found) {
                            return found;
                        }
                    }
                }

                return null;
            }
        }
        
        // 初始化场景
        function initScene() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf0f0f0);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, 800 / 600, 0.1, 1000);
            camera.position.set(5, 5, 5);
            camera.lookAt(0, 0, 0);
            
            // 创建渲染器
            const canvas = document.getElementById('testCanvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(800, 600);
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // 创建测试mesh（一个长方体，偏离中心）
            const geometry = new THREE.BoxGeometry(2, 0.5, 1);
            const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
            testMesh = new THREE.Mesh(geometry, material);
            testMesh.name = 'testMesh';
            testMesh.position.set(1, 0, 0); // 偏离原点
            scene.add(testMesh);
            
            // 添加坐标轴辅助器
            const axesHelper = new THREE.AxesHelper(3);
            scene.add(axesHelper);
            
            // 添加网格
            const gridHelper = new THREE.GridHelper(10, 10);
            scene.add(gridHelper);
            
            // 创建RouteSimulator实例
            routeSimulator = new TestRouteSimulator();
            routeSimulator.setModel(testMesh);
            
            // 渲染场景
            renderer.render(scene, camera);
            
            log('场景初始化完成');
            log(`测试mesh初始位置: (${testMesh.position.x}, ${testMesh.position.y}, ${testMesh.position.z})`);
        }
        
        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logInfo.textContent += `[${timestamp}] ${message}\n`;
            logInfo.scrollTop = logInfo.scrollHeight;
        }
        
        // 重置旋转
        window.resetRotation = function() {
            routeSimulator.rotateMeshByName('testMesh', 0);
            rotationSlider.value = 0;
            rotationValue.textContent = '0°';
            renderer.render(scene, camera);
            log('旋转已重置');
        }
        
        // 测试多次旋转
        window.testMultipleRotations = function() {
            log('开始多次旋转测试...');
            
            const angles = [45, 90, 135, 180, 225, 270, 315, 360, 0];
            let index = 0;
            
            function rotateNext() {
                if (index < angles.length) {
                    const angle = angles[index];
                    log(`第${index + 1}次旋转: ${angle}°`);
                    routeSimulator.rotateMeshByName('testMesh', angle);
                    rotationSlider.value = angle;
                    rotationValue.textContent = angle + '°';
                    renderer.render(scene, camera);
                    index++;
                    setTimeout(rotateNext, 1000);
                } else {
                    log('多次旋转测试完成');
                }
            }
            
            rotateNext();
        }
        
        // 连续旋转测试
        window.testContinuousRotation = function() {
            log('开始连续旋转测试...');
            
            let angle = 0;
            const interval = setInterval(() => {
                angle += 10;
                if (angle > 360) {
                    clearInterval(interval);
                    log('连续旋转测试完成');
                    return;
                }
                
                routeSimulator.rotateMeshByName('testMesh', angle);
                rotationSlider.value = angle;
                rotationValue.textContent = angle + '°';
                renderer.render(scene, camera);
            }, 100);
        }
        
        // 清空日志
        window.clearLog = function() {
            logInfo.textContent = '';
        }
        
        // 初始化控件
        document.addEventListener('DOMContentLoaded', function() {
            rotationSlider = document.getElementById('rotationSlider');
            rotationValue = document.getElementById('rotationValue');
            logInfo = document.getElementById('logInfo');
            
            // 滑块事件
            rotationSlider.addEventListener('input', function() {
                const angle = parseInt(this.value);
                rotationValue.textContent = angle + '°';
                routeSimulator.rotateMeshByName('testMesh', angle);
                renderer.render(scene, camera);
            });
            
            // 初始化场景
            initScene();
        });
    </script>
</body>
</html>
