<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mesh旋转性能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: inline-block;
            width: 150px;
            font-weight: bold;
        }
        
        .control-group input, .control-group select, .control-group button {
            padding: 8px 12px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .control-group button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        .control-group button:hover {
            background: #0056b3;
        }
        
        .control-group button.danger {
            background: #dc3545;
        }
        
        .control-group button.danger:hover {
            background: #c82333;
        }
        
        .stats {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats h3 {
            margin-top: 0;
            color: #333;
        }
        
        .stat-item {
            display: inline-block;
            margin-right: 30px;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-weight: bold;
            color: #666;
        }
        
        .stat-value {
            color: #007bff;
            font-size: 1.2em;
        }
        
        .log-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info {
            color: #007bff;
        }
        
        .log-entry.success {
            color: #28a745;
        }
        
        .log-entry.warning {
            color: #ffc107;
        }
        
        .log-entry.error {
            color: #dc3545;
        }
        
        .performance-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .performance-indicator.good {
            background: #28a745;
        }
        
        .performance-indicator.warning {
            background: #ffc107;
        }
        
        .performance-indicator.poor {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mesh旋转性能测试</h1>
        
        <div class="controls">
            <h3>测试控制</h3>
            
            <div class="control-group">
                <label>调试模式:</label>
                <select id="debugMode">
                    <option value="false">关闭 (高性能)</option>
                    <option value="true">开启 (详细日志)</option>
                </select>
                <button onclick="toggleDebugMode()">应用</button>
            </div>
            
            <div class="control-group">
                <label>旋转角度:</label>
                <input type="number" id="rotationAngle" value="90" min="0" max="360" step="1">
                <label>度</label>
            </div>
            
            <div class="control-group">
                <label>动画时长:</label>
                <input type="number" id="animationDuration" value="1000" min="100" max="5000" step="100">
                <label>毫秒</label>
            </div>
            
            <div class="control-group">
                <label>测试次数:</label>
                <input type="number" id="testCount" value="10" min="1" max="100" step="1">
            </div>
            
            <div class="control-group">
                <button onclick="startPerformanceTest()">开始性能测试</button>
                <button onclick="startStressTest()" class="danger">压力测试 (100次)</button>
                <button onclick="clearCache()">清理缓存</button>
                <button onclick="clearLog()">清理日志</button>
            </div>
        </div>
        
        <div class="stats">
            <h3>性能统计</h3>
            <div class="stat-item">
                <div class="stat-label">平均旋转时间:</div>
                <div class="stat-value" id="avgRotationTime">0ms</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">最快旋转时间:</div>
                <div class="stat-value" id="minRotationTime">0ms</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">最慢旋转时间:</div>
                <div class="stat-value" id="maxRotationTime">0ms</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">缓存命中率:</div>
                <div class="stat-value" id="cacheHitRate">0%</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">总测试次数:</div>
                <div class="stat-value" id="totalTests">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">性能状态:</div>
                <div class="stat-value" id="performanceStatus">
                    <span class="performance-indicator good"></span>良好
                </div>
            </div>
        </div>
        
        <div class="log-container">
            <h3>测试日志</h3>
            <div class="log-output" id="logOutput"></div>
        </div>
    </div>

    <script type="module">
        // 模拟RouteSimulator类的简化版本，用于性能测试
        class MockRouteSimulator {
            constructor() {
                this.options = {
                    debugMode: false,
                    smoothRotation: true
                };
                this.performanceStats = {
                    rotationTimes: [],
                    totalTests: 0,
                    cacheHits: 0,
                    cacheMisses: 0
                };
            }

            setDebugMode(enabled) {
                this.options.debugMode = enabled;
                log(`调试模式: ${enabled ? '开启' : '关闭'}`, 'info');
            }

            // 模拟mesh旋转性能测试
            async testMeshRotation(angle, duration) {
                const startTime = performance.now();
                
                // 模拟旋转计算的复杂度
                await this.simulateRotationCalculation(angle, duration);
                
                const endTime = performance.now();
                const rotationTime = endTime - startTime;
                
                this.performanceStats.rotationTimes.push(rotationTime);
                this.performanceStats.totalTests++;
                
                if (this.options.debugMode) {
                    log(`旋转完成: ${angle}° 耗时 ${rotationTime.toFixed(2)}ms`, 'success');
                }
                
                return rotationTime;
            }

            async simulateRotationCalculation(angle, duration) {
                // 模拟几何体计算
                const geometryCalculationTime = this.options.debugMode ? 5 : 1;
                await this.sleep(geometryCalculationTime);
                
                // 模拟矩阵计算
                const matrixCalculationTime = this.options.debugMode ? 3 : 0.5;
                await this.sleep(matrixCalculationTime);
                
                // 模拟动画帧计算
                const frameCount = duration / 16; // 假设60fps
                const frameCalculationTime = this.options.debugMode ? frameCount * 0.1 : frameCount * 0.02;
                await this.sleep(frameCalculationTime);
                
                // 模拟缓存命中/未命中
                if (Math.random() > 0.3) {
                    this.performanceStats.cacheHits++;
                } else {
                    this.performanceStats.cacheMisses++;
                }
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            getPerformanceStats() {
                const times = this.performanceStats.rotationTimes;
                if (times.length === 0) {
                    return {
                        avgTime: 0,
                        minTime: 0,
                        maxTime: 0,
                        totalTests: 0,
                        cacheHitRate: '0%'
                    };
                }

                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                const minTime = Math.min(...times);
                const maxTime = Math.max(...times);
                const totalCacheOperations = this.performanceStats.cacheHits + this.performanceStats.cacheMisses;
                const cacheHitRate = totalCacheOperations > 0 
                    ? (this.performanceStats.cacheHits / totalCacheOperations * 100).toFixed(1) + '%'
                    : '0%';

                return {
                    avgTime: avgTime.toFixed(2),
                    minTime: minTime.toFixed(2),
                    maxTime: maxTime.toFixed(2),
                    totalTests: this.performanceStats.totalTests,
                    cacheHitRate
                };
            }

            clearCache() {
                this.performanceStats.cacheHits = 0;
                this.performanceStats.cacheMisses = 0;
                log('缓存已清理', 'info');
            }

            resetStats() {
                this.performanceStats = {
                    rotationTimes: [],
                    totalTests: 0,
                    cacheHits: 0,
                    cacheMisses: 0
                };
            }
        }

        // 全局变量
        let simulator = new MockRouteSimulator();
        let isTestRunning = false;

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 更新统计信息
        function updateStats() {
            const stats = simulator.getPerformanceStats();
            
            document.getElementById('avgRotationTime').textContent = stats.avgTime + 'ms';
            document.getElementById('minRotationTime').textContent = stats.minTime + 'ms';
            document.getElementById('maxRotationTime').textContent = stats.maxTime + 'ms';
            document.getElementById('cacheHitRate').textContent = stats.cacheHitRate;
            document.getElementById('totalTests').textContent = stats.totalTests;
            
            // 更新性能状态
            const avgTime = parseFloat(stats.avgTime);
            const statusElement = document.getElementById('performanceStatus');
            
            if (avgTime < 10) {
                statusElement.innerHTML = '<span class="performance-indicator good"></span>优秀';
            } else if (avgTime < 20) {
                statusElement.innerHTML = '<span class="performance-indicator warning"></span>良好';
            } else {
                statusElement.innerHTML = '<span class="performance-indicator poor"></span>需要优化';
            }
        }

        // 切换调试模式
        window.toggleDebugMode = function() {
            const debugMode = document.getElementById('debugMode').value === 'true';
            simulator.setDebugMode(debugMode);
        };

        // 开始性能测试
        window.startPerformanceTest = async function() {
            if (isTestRunning) {
                log('测试正在进行中，请等待完成', 'warning');
                return;
            }

            isTestRunning = true;
            
            const angle = parseInt(document.getElementById('rotationAngle').value);
            const duration = parseInt(document.getElementById('animationDuration').value);
            const testCount = parseInt(document.getElementById('testCount').value);
            
            log(`开始性能测试: ${testCount}次旋转，角度${angle}°，时长${duration}ms`, 'info');
            
            const startTime = performance.now();
            
            for (let i = 0; i < testCount; i++) {
                await simulator.testMeshRotation(angle, duration);
                updateStats();
                
                // 每10次测试暂停一下，避免阻塞UI
                if ((i + 1) % 10 === 0) {
                    await simulator.sleep(10);
                    log(`已完成 ${i + 1}/${testCount} 次测试`, 'info');
                }
            }
            
            const totalTime = performance.now() - startTime;
            log(`性能测试完成，总耗时: ${totalTime.toFixed(2)}ms`, 'success');
            
            isTestRunning = false;
        };

        // 压力测试
        window.startStressTest = async function() {
            if (isTestRunning) {
                log('测试正在进行中，请等待完成', 'warning');
                return;
            }

            document.getElementById('testCount').value = '100';
            await startPerformanceTest();
        };

        // 清理缓存
        window.clearCache = function() {
            simulator.clearCache();
            updateStats();
        };

        // 清理日志
        window.clearLog = function() {
            document.getElementById('logOutput').innerHTML = '';
            simulator.resetStats();
            updateStats();
            log('日志和统计数据已清理', 'info');
        };

        // 初始化
        log('Mesh旋转性能测试工具已加载', 'success');
        log('提示：关闭调试模式可以获得更好的性能表现', 'info');
        updateStats();
    </script>
</body>
</html>
